#%%
import torch
from torch import nn


# 假设我们有一个MNIST数据批次
x = torch.zeros([5, 1, 28, 28])  # 形状：(batch_size, channels, height, width)
print(f"原始张量形状: {x.shape}")
print(f"张量维度数: {x.ndim}")  # 4维张量

# 创建示例数据
x = torch.zeros([5, 1, 28, 28])
print(f"原始形状: {x.shape}")

# start_dim=0: 从第0维开始展平（展平所有维度）
flatten0 = nn.Flatten(start_dim=0)
result0 = flatten0(x)
print(f"start_dim=0 结果: {result0.shape}")  # [3920] = 5×1×28×28

# start_dim=1: 从第1维开始展平（保持batch维度）
flatten1 = nn.Flatten(start_dim=1)
result1 = flatten1(x)
print(f"start_dim=1 结果: {result1.shape}")  # [5, 784] = [5, 1×28×28]

# start_dim=2: 从第2维开始展平（保持batch和channel维度）
flatten2 = nn.Flatten(start_dim=2)
result2 = flatten2(x)
print(f"start_dim=2 结果: {result2.shape}")  # [5, 1, 784] = [5, 1, 28×28]